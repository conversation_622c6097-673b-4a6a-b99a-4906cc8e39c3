'use client';

import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import EnhancedGridBackground from './EnhancedGridBackground';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  NoSymbolIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Smart AI Routing",
    subtitle: "when you need it",
    description: "<PERSON>ou<PERSON>ey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.",
    details: [
      "Intelligent request classification and routing",
      "Automatic model selection based on task type",
      "Real-time performance optimization",
      "Seamless provider switching"
    ],
    bgColor: "bg-blue-600",
    textColor: "text-white",
    subtitleColor: "text-blue-100",
    detailColor: "text-blue-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    subtitle: "military-grade protection",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    details: [
      "AES-256-GCM encryption for all data",
      "Zero-knowledge architecture",
      "SOC 2 Type II compliance",
      "Advanced threat detection"
    ],
    bgColor: "bg-emerald-600",
    textColor: "text-white",
    subtitleColor: "text-emerald-100",
    detailColor: "text-emerald-50"
  },
  {
    icon: ChartBarIcon,
    title: "Cost Optimization",
    subtitle: "intelligent spending",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    details: [
      "Real-time cost tracking and alerts",
      "Automatic free-tier utilization",
      "Budget optimization recommendations",
      "Multi-provider cost comparison"
    ],
    bgColor: "bg-orange-600",
    textColor: "text-white",
    subtitleColor: "text-orange-100",
    detailColor: "text-orange-50"
  },
  {
    icon: GlobeAltIcon,
    title: "300+ AI Models",
    subtitle: "unified access",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    details: [
      "Connect to any AI provider with one API",
      "Automatic failover and load balancing",
      "Real-time performance monitoring",
      "Global infrastructure deployment"
    ],
    bgColor: "bg-purple-600",
    textColor: "text-white",
    subtitleColor: "text-purple-100",
    detailColor: "text-purple-50"
  }
];

export default function FeaturesSection() {
  const [activeCard, setActiveCard] = useState(0);
  const [scrollY, setScrollY] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveCard(prev => (prev + 1) % features.length);
    }, 4000); // Auto-advance every 4 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }
      `}</style>
      <section id="features" className="relative overflow-hidden py-20" ref={sectionRef}>
      {/* Background with RouKey colors */}
      <div className="bg-gradient-to-br from-[#040716] to-[#1C051C] relative">
        {/* Enhanced Grid Background with Parallax */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="absolute inset-0"
          style={{
            transform: `translateY(${scrollY * 0.1}px)`
          }}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center mb-4">
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-4xl sm:text-5xl font-bold text-white mb-4 leading-tight"
              style={{
                transform: `translateY(${scrollY * 0.05}px)`
              }}
            >
              Enterprise-Grade
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                {' '}AI Infrastructure
              </span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-2"
              style={{
                transform: `translateY(${scrollY * 0.03}px)`
              }}
            >
              RouKey provides military-grade security, intelligent routing, and comprehensive analytics
              for the most demanding AI workloads. Built for scale, designed for performance.
            </motion.p>
          </div>

          {/* 3D Card Stack Container */}
          <div className="relative h-[400px] flex items-center justify-center perspective-1000">
            <div className="relative w-full max-w-5xl">
              {features.map((feature, index) => {
                const isActive = index === activeCard;
                const offset = index - activeCard;

                // Calculate 3D transforms
                const rotateY = offset * 15;
                const translateX = offset * 120;
                const translateZ = isActive ? 0 : Math.abs(offset) * -100;
                const scale = isActive ? 1 : 0.9 - Math.abs(offset) * 0.1;
                const opacity = Math.max(0.3, 1 - Math.abs(offset) * 0.3);

                return (
                  <motion.div
                    key={feature.title}
                    className="absolute inset-0 cursor-pointer"
                    style={{
                      transform: `
                        translateX(${translateX}px)
                        translateZ(${translateZ}px)
                        rotateY(${rotateY}deg)
                        scale(${scale})
                      `,
                      opacity: opacity,
                      zIndex: isActive ? 10 : 10 - Math.abs(offset),
                      transformStyle: 'preserve-3d'
                    }}
                    onClick={() => setActiveCard(index)}
                    whileHover={{
                      scale: scale * 1.05,
                      transition: { duration: 0.2 }
                    }}
                    animate={{
                      transform: `
                        translateX(${translateX}px)
                        translateZ(${translateZ}px)
                        rotateY(${rotateY}deg)
                        scale(${scale})
                      `,
                      opacity: opacity
                    }}
                    transition={{
                      duration: 0.6,
                      ease: "easeInOut",
                      type: "spring",
                      stiffness: 100
                    }}
                  >
                    <div
                      className="rounded-3xl p-8 h-full shadow-2xl border-2 border-white/10 hover:border-white/20 transition-colors duration-300"
                      style={{
                        backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',
                        opacity: 1
                      }}
                    >
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center h-full">
                        {/* Left Side - Content */}
                        <div>
                          <div className="flex items-center gap-4 mb-6">
                            <div className="w-14 h-14 bg-black/10 rounded-2xl flex items-center justify-center shadow-lg">
                              <feature.icon className={`h-7 w-7 ${feature.textColor}`} />
                            </div>
                            <div>
                              <h3 className={`text-2xl font-bold ${feature.textColor} mb-1`}>
                                {feature.title}
                              </h3>
                              <p className={`text-base ${feature.subtitleColor}`}>{feature.subtitle}</p>
                            </div>
                          </div>

                          <p className={`text-lg ${feature.detailColor} mb-6 leading-relaxed`}>
                            {feature.description}
                          </p>

                          <div className="space-y-3">
                            {feature.details.slice(0, 3).map((detail, detailIndex) => (
                              <div key={detailIndex} className="flex items-start gap-3">
                                <div className="w-5 h-5 bg-black/10 rounded-lg flex items-center justify-center mt-0.5">
                                  <div className={`w-1.5 h-1.5 ${feature.textColor} rounded-full`}></div>
                                </div>
                                <span className={`${feature.detailColor} text-sm leading-relaxed`}>{detail}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Right Side - Visual/Demo Area */}
                        <div className="bg-black/10 rounded-2xl p-6 border border-white/10">
                          <div className="aspect-[4/3] bg-black/5 rounded-xl flex items-center justify-center">
                            <div className="text-center">
                              <div>
                                <feature.icon className={`h-12 w-12 ${feature.textColor} mx-auto mb-3`} />
                              </div>
                              <p className={`${feature.subtitleColor} text-sm`}>Interactive demo</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Card Navigation */}
          <div className="flex justify-center items-center mt-16 space-x-6">
            {/* Previous Button */}
            <button
              onClick={() => setActiveCard(prev => prev === 0 ? features.length - 1 : prev - 1)}
              className="w-12 h-12 bg-gray-800/60 hover:bg-gray-700/60 border border-gray-600/50 rounded-full flex items-center justify-center transition-all duration-300 hover:border-[#ff6b35]/50"
            >
              <svg className="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Card Indicators */}
            <div className="flex space-x-3">
              {features.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveCard(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === activeCard
                      ? 'bg-[#ff6b35] scale-125 shadow-lg shadow-[#ff6b35]/50'
                      : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>

            {/* Next Button */}
            <button
              onClick={() => setActiveCard(prev => (prev + 1) % features.length)}
              className="w-12 h-12 bg-gray-800/60 hover:bg-gray-700/60 border border-gray-600/50 rounded-full flex items-center justify-center transition-all duration-300 hover:border-[#ff6b35]/50"
            >
              <svg className="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Auto-play indicator */}
          <div className="flex justify-center mt-6">
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse"></div>
              Auto-advancing every 4 seconds
            </div>
          </div>
        </div>
      </div>
    </section>
    </>
  );
}
